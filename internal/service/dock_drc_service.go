package service

import (
	"fmt"
	"sync"
	"time"
	control_cloud_enum "wukong-api/cloud_sdk/cloud_api/control/cloud_enum"
	control_request "wukong-api/cloud_sdk/cloud_api/control/request"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/device/request"
	wayline_cloud_enum "wukong-api/cloud_sdk/cloud_api/wayline/cloud_enum"
	"wukong-api/cloud_sdk/mqtt"
	"wukong-api/internal/cloudinfra"
	"wukong-api/internal/config"
	"wukong-api/internal/dto"
	"wukong-api/internal/enum"
	"wukong-api/internal/fileds"
	"wukong-api/internal/repo"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	dockDRCServiceOnce sync.Once
	dockDRCService     *DockDRCService
)

// DockDRCService drc
// 章节中的DRC API https://developer.dji.com/doc/cloud-api-tutorial/cn/api-reference/dock-to-cloud/mqtt/dock/dock3/drc.html
type DockDRCService struct {
	db                     *gorm.DB
	logger                 *zap.SugaredLogger
	redis                  *redis.Client
	deviceService          *DeviceService
	waylineRedisService    *WaylineRedisService
	deviceRedisService     *DeviceRedisService
	dockDRCRedisService    *DockDRCRedisService
	liveStreamRedisService *LiveStreamRedisService
	jobLogsSvc             *JobLogsService
}

func NewDockDRCService() *DockDRCService {
	dockDRCServiceOnce.Do(func() {
		dockDRCService = &DockDRCService{
			db:                     repo.GetDatabase(),
			logger:                 repo.GetLogger(),
			redis:                  repo.GetRedis(0),
			deviceService:          NewDeviceService(),
			waylineRedisService:    NewWaylineRedisService(),
			deviceRedisService:     NewDeviceRedisService(),
			dockDRCRedisService:    NewDockDRCRedisService(),
			liveStreamRedisService: NewLiveStreamRedisService(),
			jobLogsSvc:             NewJobLogsService(),
		}
	})

	return dockDRCService
}

// DeviceDrcEnter 进入指令飞行控制模式
func (s *DockDRCService) DeviceDrcEnter(dockSn string, isIgnoreCheck bool) (*int, error) {
	if !isIgnoreCheck {
		if s.checkIsInDrcMode(dockSn) {
			return nil, nil
		}
	}

	if err := s.checkDrcModeCondition(dockSn); err != nil {
		return nil, err
	}

	clientId := fmt.Sprintf("%s-%d", dockSn, time.Now().UnixMilli())

	osdFrequency := 1
	// fixme 暂时的测试
	if dockSn == "7CTDM5800BUJKJ" {
		dock, err := s.deviceService.GetDeviceBySn(dockSn)
		if err != nil {
			return nil, err
		}
		jobRecord, err := NewInspectionJobRecordService().getByWaylineJobId(dock.JobId)
		if err != nil {
			return nil, err
		}
		// 该航线为 轨道线演示航线
		if jobRecord.WaylineManageID == 96 || jobRecord.WaylineManageID == 251 || jobRecord.WaylineManageID == 256 {
			osdFrequency = 30
		}
	} else if dockSn == "8UUXN2H00A011H" {
		dock, err := s.deviceService.GetDeviceBySn(dockSn)
		if err != nil {
			return nil, err
		}
		jobRecord, err := NewInspectionJobRecordService().getByWaylineJobId(dock.JobId)
		if err != nil {
			return nil, err
		}
		// 该航线为 轨道线演示航线
		if jobRecord.WaylineManageID == 180 {
			osdFrequency = 30
		}
		osdFrequency = 30
	} else if dockSn == "4TADL330010033" {
		dock, err := s.deviceService.GetDeviceBySn(dockSn)
		if err != nil {
			return nil, err
		}
		jobRecord, err := NewInspectionJobRecordService().getByWaylineJobId(dock.JobId)
		if err != nil {
			return nil, err
		}
		// 该航线为 CAD叠加测试航线
		if jobRecord.WaylineManageID == 280 {
			osdFrequency = 30
		}
		osdFrequency = 30
	}

	req := control_request.DrcModeEnterRequest{
		MqttBroker:   getMqttBrokerWithDrc(clientId, fileds.DRC_MODE_ALIVE_SECOND),
		OsdFrequency: osdFrequency,
		HsiFrequency: 1,
	}

	serviceReply, err := cloudinfra.DrcModeEnter(dockSn, req)
	if err != nil {
		s.logger.Errorf("enter drc mode failed, dockSn: %s, err: %v", dockSn, err)
		return nil, err
	}

	if *serviceReply.Data.Result != 0 {
		return serviceReply.Data.Result, fmt.Errorf("enter drc mode failed, dockSn: %s, err code: %d", dockSn, *serviceReply.Data.Result)
	}

	_ = s.dockDRCRedisService.setDrcModeInRedis(dockSn, clientId)
	_ = s.dockDRCRedisService.setDrcControlSeqInRedis(dockSn, 0)
	_ = s.dockDRCRedisService.setDrcHeartBeatSeqInRedis(dockSn, 0)

	//s.logger.Infof("DeviceDrcEnter: enter drc mode success, dockSn: %s", dockSn)

	return serviceReply.Data.Result, nil
}

// DeviceDrcExit 退出指令飞行控制模式
func (s *DockDRCService) DeviceDrcExit(dockSn string) (*int, error) {
	dock, err := NewDeviceService().GetDeviceBySn(dockSn)
	if err != nil {
		s.logger.Errorf("DeviceDrcExit: get dock failed, dockSn: %s, err: %v", dockSn, err)
		return nil, err
	}

	if !s.deviceService.checkDockDrcMode(dockSn) {
		s.dockDRCRedisService.delDrcDataInRedis(dockSn)
		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, dock.JobId, dockSn, "无人机退出接管", false)
		//return nil, fmt.Errorf("the dock is not in flight control mode. dockSn: %s", dockSn)
		return nil, nil
	}

	serviceReply, err := cloudinfra.DrcModeExit(dockSn)
	if err != nil {
		s.logger.Errorf("exit drc mode failed, dockSn: %s, err: %v", dockSn, err)
		return nil, err
	}

	if *serviceReply.Data.Result != 0 {
		return serviceReply.Data.Result, fmt.Errorf("exit drc mode failed, dockSn: %s, err code: %d", dockSn, *serviceReply.Data.Result)
	}

	_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, dock.JobId, dockSn, "无人机退出接管", false)

	s.dockDRCRedisService.delDrcDataInRedis(dockSn)

	jobId, _ := s.waylineRedisService.GetPausedWaylineJob(dockSn)
	if jobId != "" {
		var code *int
		if code, err, _ = NewFightTaskService().UpdateFlightTaskStatus(jobId, enum.WaylineTaskStatus_RESUME); err != nil {
			return code, err
		}
	} else {
		// 无人机处于返航阶段 则直接一键返航
		runningWaylineJob, _ := s.waylineRedisService.GetRunningWaylineJob(dockSn)
		if runningWaylineJob != nil && runningWaylineJob.Output.Progress.CurrentStep != nil &&
			(runningWaylineJob.Output.Progress.CurrentStep.Value() == 24 || runningWaylineJob.Output.Progress.CurrentStep.Value() == 25) {

			var returnHomeServiceReply *mqtt.CommonTopicResponse[mqtt.MqttReply]
			returnHomeServiceReply, err = cloudinfra.ReturnHome(dockSn)
			if err != nil {
				s.logger.Errorf("return home failed, jobId: %s, err: %v", jobId, err)
				return nil, err
			}

			if *returnHomeServiceReply.Data.Result != 0 {
				return returnHomeServiceReply.Data.Result, fmt.Errorf("恢复返航失败, jobId: %s, result: %d", jobId, returnHomeServiceReply.Data.Result)
			}
		}
	}

	return serviceReply.Data.Result, nil
}

// DroneControlCommand 存储无人机控制指令的结构
type DroneControlCommand struct {
	sync.Mutex     // 添加互斥锁
	X              float64
	Y              float64
	W              float64
	H              float64
	LastUpdateTime time.Time
	StopSignal     chan struct{}
}

// 用于存储每个设备当前的控制指令
var droneCommandMap sync.Map

// DeviceDrcCmd 优化后的飞行控制指令函数
func (s *DockDRCService) DeviceDrcCmd(dto dto.DroneControlReq) error {
	dockSn := dto.DockSn

	// 检查是否处于指令飞行控制模式
	drcMode, _ := s.dockDRCRedisService.getDrcModeInRedis(dockSn)
	if drcMode == "" || !s.deviceService.checkDockDrcModeWithDrcCmd(dockSn) {
		return fmt.Errorf("the dock is not in drc mode. dockSn: %s", dockSn)
	}

	// 获取或创建控制指令对象
	cmdValue, exists := droneCommandMap.Load(dockSn)
	if exists {
		// 更新现有指令
		cmd := cmdValue.(*DroneControlCommand)
		cmd.Lock()
		cmd.X = dto.X
		cmd.Y = dto.Y
		cmd.W = dto.W
		cmd.H = dto.H
		cmd.LastUpdateTime = time.Now()
		cmd.Unlock()
	} else {
		// 创建新的控制指令并启动控制循环
		stopChan := make(chan struct{})
		cmd := &DroneControlCommand{
			X:              dto.X,
			Y:              dto.Y,
			W:              dto.W,
			H:              dto.H,
			LastUpdateTime: time.Now(),
			StopSignal:     stopChan,
		}
		droneCommandMap.Store(dockSn, cmd)

		// 启动控制循环
		go s.controlLoop(dockSn, cmd)
	}

	return nil
}

// controlLoop 持续发送控制指令的循环
func (s *DockDRCService) controlLoop(dockSn string, cmd *DroneControlCommand) {
	ticker := time.NewTicker(time.Millisecond * 100) // ~6.67Hz
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 如果超过800ms没有新指令，停止控制循环
			if time.Since(cmd.LastUpdateTime) > time.Millisecond*800 {
				droneCommandMap.Delete(dockSn)
				return
			}

			// 发送控制指令
			if err := s.sendControlCommand(dockSn, cmd); err != nil {
				s.logger.Errorf("send control command failed: %v", err)
			}

		case <-cmd.StopSignal:
			droneCommandMap.Delete(dockSn)
			return
		}
	}
}

// sendControlCommand 发送单次控制指令
func (s *DockDRCService) sendControlCommand(dockSn string, cmd *DroneControlCommand) error {
	lockKey := fmt.Sprintf(fileds.DRC_Control_Cmd_Lock, dockSn)
	lockTimeout := 2500 * time.Millisecond

	// 获取锁
	acquired, err := s.redis.SetNX(lockKey, "locked", lockTimeout).Result()
	if err != nil || !acquired {
		return fmt.Errorf("failed to acquire lock: %v", err)
	}
	defer s.redis.Del(lockKey)

	// 获取控制序列号
	seq, err := s.dockDRCRedisService.getDrcControlSeqInRedis(dockSn)
	if err != nil {
		return fmt.Errorf("get drc control seq failed: %v", err)
	}

	// 发送控制请求
	req := control_request.DroneControlRequest{
		X:   cmd.X,
		Y:   cmd.Y,
		W:   cmd.W,
		H:   cmd.H,
		Seq: seq,
	}

	if err = cloudinfra.DroneControlDown(dockSn, req); err != nil {
		return fmt.Errorf("drone control failed: %v", err)
	}

	//s.logger.Infof("==========================send control command success, dockSn: %s, cmd: %+v=======================", dockSn, req)

	// 更新序列号
	return s.dockDRCRedisService.setDrcControlSeqInRedis(dockSn, seq+1)
}

// DeviceDrcStop 飞行器急停
func (s *DockDRCService) DeviceDrcStop(dockSn string) (error, string) {
	// 避免drc链路未连接
	_, err := s.DeviceDrcEnter(dockSn, false)
	if err != nil {
		s.logger.Errorf("DeviceDrcStop: device enter drc failed, dockSn: %s, err: %v", dockSn, err)
		return err, "飞行器急停失败"
	}

	if err = cloudinfra.DroneEmergencyStopDown(dockSn); err != nil {
		s.logger.Errorf("drone emergency stop failed, dockSn: %s, err: %v", dockSn, err)
		return err, "飞行器急停失败"
	}

	dock, err := NewDeviceService().GetDeviceBySn(dockSn)
	if err != nil {
		s.logger.Errorf("DeviceDrcStop: get dock failed, dockSn: %s, err: %v", dockSn, err)
	} else {
		_ = s.jobLogsSvc.SaveJobLogByGateway(enum.JobTypeEnum_Wayline, dock.JobId, dockSn, "无人机紧急制动", false)
	}

	return nil, ""
}

func (s *DockDRCService) checkIsInDrcMode(dockSn string) bool {
	drcMode, _ := s.dockDRCRedisService.getDrcModeInRedis(dockSn)
	isInDrcMode := s.deviceService.checkDockDrcMode(dockSn)
	isAuthorityFlight := s.deviceService.checkAuthorityFlight(dockSn)

	if drcMode != "" && isInDrcMode && isAuthorityFlight {
		return true
	}

	return false
}

// checkDrcModeCondition 检查指令飞行模式条件，并且抢夺飞行控制权
func (s *DockDRCService) checkDrcModeCondition(dockSn string) error {
	dock, err := s.deviceRedisService.getDeviceOnline(dockSn)
	if err != nil {
		return fmt.Errorf("the current state of the dock does not support entering command flight mode. err: %v", err)
	}

	dockMode := s.deviceService.getDockMode(dockSn)

	if dock != nil && dockMode != nil && (dockMode.Value() == cloud_enum.DockModeCode_IDLE.Value() || dockMode.Value() == cloud_enum.WORKING.Value()) {
		var dockDrone *request.OsdDockDrone
		if droneIsOnline, _ := s.deviceRedisService.CheckDeviceOnline(dock.ChildSN); droneIsOnline {
			dockDrone, err = s.deviceRedisService.getDockDroneOsd(dock.ChildSN)
			if err != nil {
				return fmt.Errorf("the drone is not in the sky and cannot enter command flight mode. drone sn: %s, err: %v", dock.ChildSN, err)
			}
		}

		if dockDrone == nil || *dockDrone.Elevation <= 0 {
			return fmt.Errorf("the drone is not in the sky and cannot enter command flight mode. drone sn: %s", dock.ChildSN)
		}
	} else {
		return fmt.Errorf("the current state of the dock does not support entering command flight mode")
	}

	if _, err = NewDockControlService().SeizeFlightAuthority(dockSn); err != nil {
		return err
	}

	return nil
}

// drcStatusNotifyHandle DRC 链路状态通知
func (s *DockDRCService) drcStatusNotifyHandle(req mqtt.TopicEventsRequest[control_request.DrcStatusNotify]) error {
	gatewaySn := req.Gateway

	dock, _ := s.deviceRedisService.getDeviceOnline(gatewaySn)
	if dock != nil && control_cloud_enum.DISCONNECTED == *req.Data.DrcState {
		drcMode, _ := s.dockDRCRedisService.getDrcModeInRedis(gatewaySn)
		if drcMode != "" {
			// 重连
			s.logger.Infof("drcStatusNotifyHandle, retry to reconnect drc, gatewaySn: %s", gatewaySn)
			if _, err := s.DeviceDrcEnter(gatewaySn, true); err != nil {
				s.logger.Errorf("drcStatusNotifyHandle, retry to reconnect drc failed, gatewaySn: %s, err: %v", gatewaySn, err)
				return err
			}
			s.logger.Infof("drcStatusNotifyHandle, retry to reconnect drc success, gatewaySn: %s", gatewaySn)
		}
	}

	if req.NeedReply == 1 {
		err := cloudinfra.EventsReply(fileds.Drc_Status_Notify, gatewaySn, req.Bid, req.Tid)
		if err != nil {
			s.logger.Errorf("drcStatusNotifyHandle, EventsReply failed, gatewaySn: %s, err: %v", gatewaySn, err)
			return err
		}
	}

	return nil
}

func (s *DockDRCService) DrcCameraOsdInfoPushHandle(gatewaySn string, req mqtt.TopicDrcRequest[control_request.DrcCameraOsd]) error {
	err := s.dockDRCRedisService.setDrcCamera(gatewaySn, req.Data)
	if err != nil {
		s.logger.Errorf("DrcCameraOsdInfoPushHandle, setDrcCamera failed, gatewaySn: %s, err: %v", gatewaySn, err)
		return err
	}

	return nil
}

// osdInfoPushHandle DRC高频 osd 信息上报
func (s *DockDRCService) osdInfoPushHandle(gatewaySn string, req mqtt.TopicDrcRequest[control_request.OsdDrc]) error {
	// 实时，检查ws订阅

	// 非实时

	job, err := s.liveStreamRedisService.getLiveStreamingJob(gatewaySn)
	if err != nil {
		return err
	}
	if job == nil || req.Timestamp < job.LiveStartTime {
		return nil
	}

	droneOsd, err := s.deviceRedisService.getDockDroneOsd(job.DroneSn)
	if err != nil {
		return err
	}

	var attitudePitch, attitudeRoll float64
	if droneOsd != nil {
		if droneOsd.AttitudePitch != nil {
			attitudePitch = *droneOsd.AttitudePitch
		}
		if droneOsd.AttitudeRoll != nil {
			attitudeRoll = *droneOsd.AttitudeRoll
		}
	}

	attitudeInfo := dto.DroneAttitudeInfo{
		OsdDrc:        req.Data,
		AttitudePitch: attitudePitch,
		AttitudeRoll:  attitudeRoll,
	}

	err = s.waylineRedisService.addDroneAttitudeInfo(job.JobID, attitudeInfo, req.Timestamp)
	if err != nil {
		s.logger.Errorf("osdInfoPushHandle: addDroneAttitudeInfo failed, jobId: %s, osd timestamp: %d, err: %v", job.JobID, req.Timestamp, err)
		return err
	}

	return nil
}

func (s *DockDRCService) droneControlHandle(gatewaySn string, req mqtt.TopicDrcDroneControlRequest) error {
	waylineErrorCode := wayline_cloud_enum.WaylineErrorCodeEnum(req.Data.Result)

	if waylineErrorCode == wayline_cloud_enum.EMERGENCY_STOP_STATUS || waylineErrorCode == wayline_cloud_enum.SEQ_NUMBER_SMALLER_THAN_LAST || waylineErrorCode == wayline_cloud_enum.NO_PAYLOAD_CONTROL {
		// 重置seq为0
		// 获取锁
		lockKey := fmt.Sprintf(fileds.DRC_Control_Cmd_Lock, gatewaySn)
		lockTimeout := 2500 * time.Millisecond
		lockRetryInterval := 100 * time.Millisecond
		lockMaxRetryTime := 3 * time.Second

		start := time.Now()
		for {
			acquired, err := s.redis.SetNX(lockKey, "locked", lockTimeout).Result()
			if err != nil {
				return err
			}
			if acquired {
				break
			}

			if time.Since(start) > lockMaxRetryTime {
				err = fmt.Errorf("droneControlHandle: could not acquire lock for device %s after %s", gatewaySn, lockMaxRetryTime)
				s.logger.Warnf(err.Error())
				return nil
			}

			time.Sleep(lockRetryInterval)
		}
		defer s.redis.Del(lockKey)

		if waylineErrorCode == wayline_cloud_enum.NO_PAYLOAD_CONTROL {
			isAuthorityFlight := s.deviceService.checkAuthorityFlight(gatewaySn)
			s.logger.Infof("droneControlHandle: checkAuthorityFlight, gatewaySn: %s, isAuthorityFlight: %v", gatewaySn, isAuthorityFlight)

			code, err := NewDockControlService().FlightAuthorityGrab(gatewaySn)
			if err != nil {
				if code != nil && *code != 0 {
					return fmt.Errorf("droneControlHandle: FlightAuthorityGrab failed, gatewaySn: %s, err: %v, code: %d", gatewaySn, err, *code)
				} else {
					s.logger.Errorf("droneControlHandle: FlightAuthorityGrab failed, gatewaySn: %s, err: %v", gatewaySn, err)
				}
				return err
			}
		}

		err := s.dockDRCRedisService.setDrcControlSeqInRedis(gatewaySn, 0)
		if err != nil {
			s.logger.Errorf("droneControlHandle: setDrcControlSeqInRedis failed, gatewaySn: %s, err: %v", gatewaySn, err)
			return err
		}

		//s.logger.Infof("droneControlHandle: reset seq success, gatewaySn: %s", gatewaySn)
	}

	return nil
}

func getMqttBrokerWithDrc(clientId string, age int64) control_request.DrcModeMqttBroker {
	cnf := config.GetConfig()
	mqttConf := cnf.Mqtt.Drc
	addr := fmt.Sprintf("tcp://%s:%d", mqttConf.Broker, mqttConf.Port)

	return control_request.DrcModeMqttBroker{
		Address:    addr,
		Username:   mqttConf.User,
		Password:   mqttConf.Password,
		ClientId:   clientId,
		ExpireTime: time.Now().Unix() + age,
		EnableTls:  false,
	}
}
