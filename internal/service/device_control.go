package service

import (
	"math"
	"sync"
	"time"
	"wukong-api/cloud_sdk/cloud_api/device/cloud_enum"
	"wukong-api/cloud_sdk/cloud_api/device/request"
	live_stream_cloud_enum "wukong-api/cloud_sdk/cloud_api/live_stream/cloud_enum"
	"wukong-api/internal/dto"
	"wukong-api/internal/fileds"
	"wukong-api/internal/pb"
	"wukong-api/internal/repo"
	"wukong-api/pkg/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	deviceControlServiceOnce sync.Once
	deviceControlService     *DeviceControlService
)

type DeviceControlService struct {
	db                 *gorm.DB
	logger             *zap.SugaredLogger
	deviceRedisService *DeviceRedisService
}

func NewDeviceControlService() *DeviceControlService {
	deviceControlServiceOnce.Do(func() {
		deviceControlService = &DeviceControlService{
			db:                 repo.GetDatabase(),
			logger:             repo.GetLogger(),
			deviceRedisService: NewDeviceRedisService(),
		}
	})
	return deviceControlService
}

// HandleGimbalControl 处理云台控制
func (s *DeviceControlService) HandleGimbalControl(dockSn, droneSn string, gimbalControl *pb.GimbalControl) error {
	dockControlSvc := NewDockControlService()
	liveStreamRedisSvc := NewLiveStreamRedisService()
	liveStreamSvc := NewLiveStreamService()
	drcRedisSvc := NewDockDRCRedisService()

	// fixme 先写死负载索引
	payloadIndex := "99-0-0"

	// 云台模式切换
	if gimbalControl.GimbalType == pb.GimbalType_GIMBAL_TYPE_ZOOM || gimbalControl.GimbalType == pb.GimbalType_GIMBAL_TYPE_WIDE {
		var cameraType live_stream_cloud_enum.LensChangeVideoTypeEnum
		if gimbalControl.GimbalType == pb.GimbalType_GIMBAL_TYPE_ZOOM {
			cameraType = live_stream_cloud_enum.LensChangeVideo_ZOOM
		} else {
			cameraType = live_stream_cloud_enum.LensChangeVideo_WIDE
		}

		// 检查是否已为该模式
		isIgnore := false

		liveStreaming, _ := liveStreamRedisSvc.getLiveStreaming(droneSn)
		if liveStreaming != nil && liveStreaming.LiveStatus != nil {
			for _, status := range liveStreaming.LiveStatus {
				// 简化检查逻辑，暂时跳过复杂的云台检查
				if status.VideoType.Value() == cameraType.Value() {
					isIgnore = true
					break
				}
			}
		}

		if !isIgnore {
			err, errMsg := liveStreamSvc.LiveLensChange(dockSn, "99-0-0", cameraType)
			if err != nil {
				s.logger.Errorf("LiveLensChange failed, err: %v, errMsg: %s", err, errMsg)
				// return
			} else {
				s.logger.Infof("do LiveLensChange success, cameraType: %s", cameraType.Value())
			}

			time.Sleep(300 * time.Millisecond)
		}
	}

	// 云台角度调整
	if gimbalControl.PitchAdjust != 0 || gimbalControl.YawAdjust != 0 {
		s.logger.Infof("执行云台角度调整 - Pitch: %f, Yaw: %f",
			gimbalControl.PitchAdjust, gimbalControl.YawAdjust)

		// pitch和yaw每次只能调整15度，传入的参数需要计算doNumber，然后把每次的要调的度数算好，累计能达到调整读数的要求
		pitchAdjust := gimbalControl.PitchAdjust
		yawAdjust := gimbalControl.YawAdjust

		// 计算需要调整的次数，每次最多15度
		maxAdjustPerStep := 15.0
		pitchSteps := int(math.Abs(pitchAdjust)/maxAdjustPerStep) + 1
		yawSteps := int(math.Abs(yawAdjust)/maxAdjustPerStep) + 1
		totalSteps := pitchSteps
		if yawSteps > pitchSteps {
			totalSteps = yawSteps
		}

		// 计算每步的调整量
		pitchSpeedPerStep := pitchAdjust / float64(totalSteps)
		yawSpeedPerStep := yawAdjust / float64(totalSteps)

		for i := 0; i < totalSteps; i++ {
			payloadCmd := dto.PayloadCommandsReq{
				DockSn: dockSn,
				Method: fileds.Camera_Screen_Drag,
				Data: request.DronePayloadRequest{
					PayloadIndex: payloadIndex,
					Locked:       &[]bool{false}[0],
					PitchSpeed:   &pitchSpeedPerStep,
					YawSpeed:     &yawSpeedPerStep,
				},
			}

			_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
			if err != nil {
				s.logger.Errorf("camera_screen_drag failed, do_time: %d, pitch_speed: %v, yaw_speed: %v, err: %v, errMsg: %s", i, pitchSpeedPerStep, yawSpeedPerStep, err, errMsg)
				continue
			} else {
				s.logger.Infof("camera_screen_drag success, do_time: %d, pitch_speed: %v, yaw_speed: %v", i, pitchSpeedPerStep, yawSpeedPerStep)
			}

			time.Sleep(300 * time.Millisecond)
		}
	}

	// 焦距调整
	if gimbalControl.ZoomType == pb.ZoomType_ZOOM_TYPE_IN || gimbalControl.ZoomType == pb.ZoomType_ZOOM_TYPE_OUT {
		// 获取当前变焦倍数
		cameraOsd, err := drcRedisSvc.getDrcCamera(dockSn)
		if err != nil {
			s.logger.Errorf("get camera osd failed, dockSn: %s, err: %v", dockSn, err)
			// return
		} else if cameraOsd == nil {
			s.logger.Errorf("camera osd is nil, dockSn: %s", dockSn)
			// return
		}

		var currentZoomFactor float64
		if cameraOsd != nil {
			currentZoomFactor = cameraOsd.ZoomLense.ZoomFactor
		}

		setZoomFactor := currentZoomFactor

		if gimbalControl.ZoomType == pb.ZoomType_ZOOM_TYPE_IN {
			// 放大
			setZoomFactor += 1
		} else {
			// 缩小
			setZoomFactor -= 1
		}

		payloadCmd := dto.PayloadCommandsReq{
			DockSn: dockSn,
			Method: fileds.CAMERA_FOCAL_LENGTH_SET,
			Data: request.DronePayloadRequest{
				PayloadIndex: payloadIndex,
				CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
				ZoomFactor:   &setZoomFactor,
			},
		}

		_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
		if err != nil {
			s.logger.Errorf("zoom failed, dockSn: %s, err: %v, errMsg: %s", dockSn, err, errMsg)
			// return
		} else {
			s.logger.Infof("zoom success, dockSn: %s, setZoomFactor: %v", dockSn, setZoomFactor)
		}
		time.Sleep(300 * time.Millisecond)
	}

	return nil
}

// HandlePhotoCapture 处理拍照控制
func (s *DeviceControlService) HandlePhotoCapture(dockSn string) error {
	dockControlSvc := NewDockControlService()
	//liveStreamSvc := NewLiveStreamService()

	// fixme 先写死负载索引
	payloadIndex := "99-0-0"

	// 执行拍照命令
	payloadCmd := dto.PayloadCommandsReq{
		DockSn: dockSn,
		Method: fileds.CAMERA_PHOTO_TAKE,
		Data: request.DronePayloadRequest{
			PayloadIndex: payloadIndex,
		},
	}

	_, err, errMsg := dockControlSvc.PayloadCmd(payloadCmd)
	if err != nil {
		s.logger.Errorf("HandlePhotoCapture failed, 拍照失败: %v, errMsg: %s", err, errMsg)
		return err
	}

	time.Sleep(200 * time.Millisecond)

	// 变焦1x
	_, err, errMsg = dockControlSvc.PayloadCmd(dto.PayloadCommandsReq{
		DockSn: dockSn,
		Method: fileds.CAMERA_FOCAL_LENGTH_SET,
		Data: request.DronePayloadRequest{
			PayloadIndex: payloadIndex,
			CameraType:   cloud_enum.FindCameraTypeEnum(cloud_enum.CameraType_ZOOM.Value()),
			ZoomFactor:   utils.Float64Ptr(1),
		}})
	if err != nil {
		s.logger.Errorf("HandlePhotoCapture failed, 变焦失败, gatewaySn: %s, err: %v, errMsg: %s", dockSn, err, errMsg)
		return err
	}

	time.Sleep(200 * time.Millisecond)

	// 云台回中
	_, err, errMsg = dockControlSvc.PayloadCmd(dto.PayloadCommandsReq{
		DockSn: dockSn,
		Method: fileds.GIMBAL_RESET,
		Data: request.DronePayloadRequest{
			PayloadIndex: payloadIndex,
			ResetMode:    cloud_enum.FindGimbalResetModeEnum(cloud_enum.RECENTER.Value()),
		},
	})
	if err != nil {
		s.logger.Errorf("HandlePhotoCapture failed, 云台回中, gatewaySn: %s, err: %v, errMsg: %s", dockSn, err, errMsg)
		return err
	}

	time.Sleep(200 * time.Millisecond)

	// 广角
	//err, errMsg = liveStreamSvc.LiveLensChange(dockSn, payloadIndex, live_stream_cloud_enum.LensChangeVideo_WIDE)
	//if err != nil {
	//	s.logger.Errorf("HandlePhotoCapture faild, LiveLensChange failed, gatewaySn: %s, err: %v, errMsg: %s", dockSn, err, errMsg)
	//	return err
	//}

	s.logger.Info("拍照成功")
	return nil
}

func (s *DeviceControlService) HandleDroneControl(dockSn string, droneControl *pb.DroneControl) error {
	dockDRCSvc := NewDockDRCService()

	// Y字段每次最多为1，如果大于1则需要分多次执行
	yValue := droneControl.Y
	maxYPerStep := 1.0

	// 如果Y的绝对值小于等于1，直接执行一次
	if math.Abs(yValue) <= maxYPerStep {
		droneControlReq := dto.DroneControlReq{
			DockSn: dockSn,
			X:      droneControl.X,
			Y:      droneControl.Y,
			H:      droneControl.H,
			W:      droneControl.W,
		}

		err := dockDRCSvc.DeviceDrcCmd(droneControlReq)
		if err != nil {
			s.logger.Errorf("drone control failed, dockSn: %s, err: %v", dockSn, err)
			return err
		}

		time.Sleep(1 * time.Second)

		return nil
	}

	// 计算需要执行的次数
	totalSteps := int(math.Abs(yValue)/maxYPerStep) + 1

	// 计算每步的Y值
	yPerStep := yValue / float64(totalSteps)

	for i := 0; i < totalSteps; i++ {
		droneControlReq := dto.DroneControlReq{
			DockSn: dockSn,
			X:      droneControl.X,
			Y:      yPerStep,
			H:      droneControl.H,
			W:      droneControl.W,
		}

		err := dockDRCSvc.DeviceDrcCmd(droneControlReq)
		if err != nil {
			s.logger.Errorf("drone control failed, do_time: %d, y_value: %v, err: %v", i, yPerStep, err)
			continue
		} else {
			s.logger.Infof("drone control success, do_time: %d, y_value: %v", i, yPerStep)
		}

		time.Sleep(1 * time.Second)
	}

	return nil
}
